import type { Express } from 'express';
import cron from 'node-cron';

import { currencies } from '@api/lib/constants/currencies';
import { Rate } from '@api/models';
import { fetchCurrenciesTask } from '@api/services/tasks';
import { Env } from '@api/services';
const tz = 'America/Los_Angeles';
const everyDay = '0 3 * * *'; // 03:00 at Los Angeles timezone
// const everyMinute = '* * * * *';

export default (router: Express) => {
  cron.schedule(
    everyDay,
    () => {
      console.log(`Running a job at 03:00 at ${tz} timezone`);

      fetchCurrenciesTask()
        .then((currencyData) => {
          const savePromises = [];

          for (let i = 0, len = currencyData.length; i < len; i++) {
            const item = currencyData[i];
            const currency = currencies.find((c) => c.code === item.code);

            if (currency) {
              const rate = Rate({
                id: currency.id,
                code: currency.code,
                rate: Math.round(item.rate * 10000000000) / 10000000000, // round up to 10 decimal places
              });

              savePromises.push(rate.save());
            }
          }

          Promise.all(savePromises).catch((e) => {
            console.log(e);
            throw e;
          });
        })
        .catch((err: unknown) => {
          const { message } = err as Error;
          console.error({ error: message });
          // res.status(500).json({ error: e.message });
        });
    },
    {
      scheduled: true,
      timezone: tz,
    },
  );

  const namespace = `${Env.api.namespace}/tasks`;

  // @path GET /api/v1/tasks/currencies
  router.get(`${namespace}/currencies`, (req, res) => {
    fetchCurrenciesTask()
      .then((currencyData) => {
        res.json(currencyData);
      })
      .catch((err: unknown) => {
        const { message } = err as Error;
        res.status(500).json({ error: message });
      });
  });

  // @path POST /api/v1/tasks/currencies/update
  router.post(`${namespace}/currencies/update`, (req, res) => {
    fetchCurrenciesTask()
      .then((currencyData) => {
        const savePromises = [];

        for (let i = 0, len = currencyData.length; i < len; i++) {
          const item = currencyData[i];
          const currency = currencies.find((c) => c.code === item.code);

          if (currency) {
            const rate = Rate({
              id: currency.id,
              code: currency.code,
              rate: item.rate,
            });

            savePromises.push(rate.save());
          }
        }

        Promise.all(savePromises)
          .then(() => {
            res.status(204).json({});
          })
          .catch((e) => {
            throw e;
          });
      })
      .catch((err: unknown) => {
        const { message } = err as Error;
        res.status(500).json({ error: message });
      });
  });
};
