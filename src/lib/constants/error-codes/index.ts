export const ERROR_AUTH = {
  EXISTS: 'auth/email-taken',
  NOT_FOUND: 'auth/user-not-found',
  NOT_LOGGED_IN: 'auth/not-logged-in',
  PASS: 'auth/wrong-password',
  SESSION_DESTROY: 'auth/session-destroy-error',
  SESSION: 'auth/session-error',
  TOKEN: 'auth/no-token',
};

export const ERROR_ACCOUNT = {
  CREATE: 'account/create-error',
  DELETE: 'account/delete-error',
  FETCH: 'account/fetch-error',
  NOT_FOUND: 'account/not-found',
  PATCH_ALL: 'account/patch-all-error',
  PATCH_TYPE_ALL: 'account/patch-type-all-error',
  PATCH: 'account/patch-error',
  UPDATE: 'account/update-error',
};

export const ERROR_GOAL = {
  CREATE: 'goal/create-error',
  DELETE: 'goal/delete-error',
  FETCH: 'goal/fetch-error',
  NOT_FOUND: 'goal/not-found',
  UPDATE: 'goal/update-error',
  PATCH_TYPE_ALL: 'goal/patch-type-all-error',
};

export const ERROR_HOLDING = {
  CREATE: 'holding/create-error',
  DELETE: 'holding/delete-error',
  FETCH: 'holding/fetch-error',
  NOT_FOUND: 'holding/not-found',
  PATCH_ALL: 'holding/patch-all-error',
  PATCH_TYPE_ALL: 'holding/patch-type-all-error',
  PATCH_TYPE: 'holding/patch-type-error',
  PATCH: 'holding/patch-error',
  SAVE: 'holding/save-error',
  UPDATE: 'holding/update-error',
};

export const ERROR_RATE = {
  FETCH: 'rate/fetch-error',
};

export const ERROR_REPORT = {
  FETCH: 'report/fetch-error',
  CREATE: 'report/create-error',
  SAVE: 'report/save-error',
  SAVE_ALL: 'report/save-all-error',
  UPDATE: 'report/update-error',
  DELETE: 'report/delete-error',
};

export const ERROR_USER = {
  FETCH: 'user/fetch-error',
  NOT_FOUND: 'user/not-found',
  SAVE: 'user/save-error',
  UPDATE: 'user/update-error',
  WRONG_UID: 'user/wrong-uid',
};
