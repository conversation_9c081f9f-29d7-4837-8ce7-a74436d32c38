import type { RowDataPacket } from 'mysql2';

import { toJson } from '@api/lib/utils';
import SqlAdapter, { SqlAdapterOptions } from '@api/adapters/sql';
import { ACCOUNT_TYPES, ACCOUNT_TAX_TREATMENTS } from '@api/lib/constants/accounts';

const table = 'accounts';
const adapter = SqlAdapter({ table });

type AccountProps = AccountJson & {
  id?: number;
};

const Account = (attrs: Partial<AccountProps>): AccountI => {
  const instance: Partial<AccountI> = {
    description: attrs.description,
    id: attrs.id,
    name: attrs.name,
    taxTreatment:
      attrs.taxTreatment && Object.values(ACCOUNT_TAX_TREATMENTS).includes(attrs.taxTreatment as any)
        ? attrs.taxTreatment
        : ACCOUNT_TAX_TREATMENTS.TAXABLE,
    type: attrs.type && Object.values(ACCOUNT_TYPES).includes(attrs.type as any) ? attrs.type : ACCOUNT_TYPES.SELF_DIRECTED,
    withholdingTax: attrs.withholdingTax,
    uid: attrs.uid,
  };

  Object.defineProperty(instance, 'isNew', {
    enumerable: false,
    value: !instance.id,
  });

  Object.defineProperty(instance, 'toJson', {
    enumerable: false,
    value: () => toJson(instance),
  });

  /**
   * Smart save: create/update in one
   */
  Object.defineProperty(instance, 'save', {
    enumerable: false,
    value: () => {
      if (instance.isNew) {
        return adapter.create(instance).then((result: RowDataPacket) => {
          if (result && result.insertId) {
            instance.id = result.insertId;
          }

          return instance;
        });
      } else {
        return adapter.update(instance, { where: { id: instance.id, uid: instance.uid } }).then(() => instance);
      }
    },
  });

  /**
   * import/create
   */
  Object.defineProperty(instance, 'import', {
    enumerable: false,
    value: () =>
      adapter.create(instance).then((result: RowDataPacket) => {
        if (result && result.insertId) {
          instance.id = result.insertId;
        }
        return instance;
      }),
  });

  return Object.seal(instance as AccountI);
};

Account.findOne = (options: SqlAdapterOptions) =>
  adapter.findOne(options).then((record) => (record ? Account(record as AccountJson) : null));

Account.findAll = (options: SqlAdapterOptions) => {
  return adapter.findAll(options).then((records) => {
    return records?.map((record: AccountJson) => Account(record));
  });
};

Account.delete = ({ id, uid }) => adapter.delete({ where: { id, uid } });
Account.deleteAll = (uid: string) => adapter.delete({ where: { uid } });

export default Account;
