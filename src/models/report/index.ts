import SqlAdapter, { SqlAdapterOptions } from '@api/adapters/sql';
import { Uuid, toJson } from '@api/lib/utils';

const table = 'reports';
const adapter = SqlAdapter({ table });

const Report = ({ id, uid, month, year, holdings, isLocked }: Partial<ReportJson> = {}) => {
  const date = new Date();
  const currentYear = date.getFullYear();
  const currentMonth = date.getMonth();

  const instance: Partial<ReportI> = {
    id,
    uid,
    year: year ?? currentYear,
    month: month ?? currentMonth,
    holdings,
    isLocked: Boolean(isLocked),
  };

  Object.defineProperty(instance, 'isNew', {
    enumerable: false,
    value: !instance.id,
  });

  Object.defineProperty(instance, 'toJson', {
    enumerable: false,
    value: () => toJson(instance),
  });

  /**
   * Smart save: create/update in one
   */
  Object.defineProperty(instance, 'save', {
    enumerable: false,
    value: () => {
      if (instance.isNew) {
        instance.id = Uuid();

        return adapter.create(instance).then(() => instance);
      } else {
        return adapter.update(instance, { where: { id: instance.id, uid: instance.uid } }).then(() => instance);
      }
    },
  });

  /**
   * import/create
   */
  Object.defineProperty(instance, 'import', {
    enumerable: false,
    value: () => {
      return adapter.create(instance).then(() => instance);
    },
  });

  return Object.seal(instance) as ReportI;
};

Report.findOne = (options: SqlAdapterOptions) => adapter.findOne(options).then((record) => (record ? Report(record as ReportJson) : null));

Report.findAll = (options: SqlAdapterOptions) => {
  return adapter.findAll(options).then((records) => {
    return records ? records.map((record: ReportJson) => Report(record)) : [];
  });
};

Report.delete = ({ id, uid }) => adapter.delete({ where: { id, uid } });
Report.deleteAll = (uid: string) => adapter.delete({ where: { uid } });

export default Report;
