import SqlAdapter, { SqlAdapterOptions } from '@api/adapters/sql';
import { toJson } from '@api/lib/utils';

const table = 'currency_rates';
const adapter = SqlAdapter({ table });

const Rate = ({ id, code, dtUpdated, rate }: Partial<RateJson>): RateI => {
  const instance = {
    code: String(code),
    dtUpdated: Number(dtUpdated),
    id: Number(id),
    rate: Number(rate),
  };

  Object.defineProperty(instance, 'toJson', {
    enumerable: false,
    value: () => toJson(instance),
  });

  /**
   * Smart save: create/update in one
   */
  Object.defineProperty(instance, 'save', {
    enumerable: false,
    value: () => {
      instance.dtUpdated = Math.floor(Date.now() / 1000);
      return adapter.update(instance, { where: { id: instance.id } });
    },
  });

  return Object.seal(instance as RateI);
};

Rate.findOne = (options: SqlAdapterOptions) => adapter.findOne(options).then((record) => (record ? Rate(record as RateJson) : record));

Rate.findAll = (options?: SqlAdapterOptions) => {
  return adapter.findAll(options).then((records) => (records ? records.map((record: RateJson) => Rate(record)) : []));
};

export default Rate;
