import type { RowDataPacket } from 'mysql2';

import type { SqlAdapterOptions } from '@api/adapters/sql';
import SqlAdapter from '@api/adapters/sql';
import { Uuid, toJson } from '@api/lib/utils';
import currencies, { CURRENCY_USD } from '@api/lib/constants/currencies';

const table = 'goals';
const adapter = SqlAdapter({ table });

const Goal = ({ currency, dividends, id, networth, uid }: Partial<GoalJson> = {}): GoalI => {
  const instance: Partial<GoalI> = {
    id,
    uid,
    currency: (currency && currencies.find((id) => id === Number(currency)) ? currency : CURRENCY_USD) as number,
    dividends,
    networth,
  };

  Object.defineProperty(instance, 'isNew', {
    enumerable: false,
    value: !instance.id,
  });

  Object.defineProperty(instance, 'toJson', {
    enumerable: false,
    value: () => toJson(instance),
  });

  /**
   * Smart save: create/update in one
   */
  Object.defineProperty(instance, 'save', {
    enumerable: false,
    value: () => {
      if (instance.isNew) {
        instance.id = Uuid();

        return adapter.create(instance).then(() => instance);
      } else {
        return adapter.update(instance, { where: { id: instance.id, uid: instance.uid } }).then(() => instance);
      }
    },
  });

  /**
   * import/create
   */
  Object.defineProperty(instance, 'import', {
    enumerable: false,
    value: () => adapter.create(instance).then(() => instance),
  });

  return Object.seal(instance) as GoalI;
};

Goal.findOne = (options: SqlAdapterOptions) =>
  adapter.findOne(options).then((record) => {
    return record ? Goal(record as GoalJson) : record;
  });

Goal.findAll = (options: SqlAdapterOptions): Promise<GoalI[]> => {
  return adapter.findAll(options).then((records = []): GoalI[] => {
    return records.map((record: RowDataPacket) => (record ? Goal(record as GoalJson) : record));
  });
};

Goal.delete = ({ id, uid }): Promise<unknown> => adapter.delete({ where: { id, uid } });

Goal.deleteAll = (uid: string) => adapter.delete({ where: { uid } });

export default Goal;
