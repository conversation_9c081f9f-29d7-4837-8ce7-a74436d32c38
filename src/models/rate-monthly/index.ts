import type { RowDataPacket } from 'mysql2';

import SqlAdapter, { SqlAdapterOptions } from '@api/adapters/sql';
import { toJson } from '@api/lib/utils';

const table = 'currency_rates_monthly';
const adapter = SqlAdapter({ table });

type RateMonthlyProps = RateMonthlyJson & {
  id?: number;
};

const RateMonthly = (attrs: Partial<RateMonthlyProps>): RateMonthlyI => {
  const instance: Partial<RateMonthlyI> = {
    currency_id: attrs.currency_id,
    id: attrs.id,
    month_end_date: attrs.month_end_date,
    rate: attrs.rate,
  };

  Object.defineProperty(instance, 'isNew', {
    enumerable: false,
    value: !instance.id,
  });

  Object.defineProperty(instance, 'toJson', {
    enumerable: false,
    value: () => toJson(instance),
  });

  /**
   * Smart save: create/update in one
   */
  Object.defineProperty(instance, 'save', {
    enumerable: false,
    value: () => {
      if (instance.isNew) {
        // Filter out undefined values for create operation
        const createData = Object.fromEntries(Object.entries(instance).filter(([_, value]) => value !== undefined));

        return adapter.create(createData).then((result: RowDataPacket) => {
          if (result && result.insertId) {
            instance.id = result.insertId;
          }

          return instance;
        });
      } else {
        return adapter.update(instance, { where: { id: instance.id } }).then(() => instance);
      }
    },
  });

  return Object.seal(instance as RateMonthlyI);
};

RateMonthly.findOne = (options: SqlAdapterOptions) =>
  adapter.findOne(options).then((record) => (record ? RateMonthly(record as RateMonthlyJson) : record));

RateMonthly.findAll = (options?: SqlAdapterOptions) => {
  return adapter.findAll(options).then((records) => {
    return records?.map((record: RateMonthlyJson) => RateMonthly(record));
  });
};

export default RateMonthly;
