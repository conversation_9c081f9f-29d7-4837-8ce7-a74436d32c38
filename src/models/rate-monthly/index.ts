import SqlAdapter, { SqlAdapterOptions } from '@api/adapters/sql';
import { toJson } from '@api/lib/utils';

const table = 'currency_rates_monthly';
const adapter = SqlAdapter({ table });

const RateMonthly = ({ currency_id, id, month_end_date, rate }: Partial<RateMonthlyJson>): RateMonthlyI => {
  const instance = {
    currency_id: Number(currency_id),
    id: Number(id),
    month_end_date: String(month_end_date),
    rate: Number(rate),
  };

  Object.defineProperty(instance, 'toJson', {
    enumerable: false,
    value: () => toJson(instance),
  });

  /**
   * Smart save: create/update in one
   */
  Object.defineProperty(instance, 'save', {
    enumerable: false,
    value: () => {
      if (instance.id) {
        return adapter.update(instance, { where: { id: instance.id } });
      } else {
        return adapter.create(instance);
      }
    },
  });

  return Object.seal(instance as RateMonthlyI);
};

RateMonthly.findOne = (options: SqlAdapterOptions) =>
  adapter.findOne(options).then((record) => (record ? RateMonthly(record as RateMonthlyJson) : record));

RateMonthly.findAll = (options?: SqlAdapterOptions) => {
  return adapter.findAll(options).then((records) => (records ? records.map((record: RateMonthlyJson) => RateMonthly(record)) : []));
};

export default RateMonthly;
