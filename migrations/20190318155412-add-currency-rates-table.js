'use strict';

let type;
const table = 'currency_rates';

/**
 * We receive the dbmigrate dependency from dbmigrate initially.
 * This enables us to not have to rely on NODE_PATH.
 */
export const setup = (options) => {
  type = options.dbmigrate.dataType;
};

export const up = (db) =>
  db.createTable(table, {
    columns: {
      id: {
        type: type.INTEGER,
        length: 3,
        notNull: true,
        primaryKey: true,
        unsigned: true,
      },
      code: {
        type: type.STRING,
        length: 3,
      },
      dtUpdated: {
        type: type.INTEGER,
        length: 10,
        unsigned: true,
      },
      rate: {
        type: type.DECIMAL,
        precision: '20',
        scale: '10',
      },
    },
    ifNotExists: true,
    charset: 'utf8mb4',
    collate: 'utf8mb4_unicode_ci',
    engine: 'InnoDB',
  });

export const down = (db) => db.dropTable(table);

export const _meta = {
  version: 1,
};
