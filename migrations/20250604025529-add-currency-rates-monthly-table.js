'use strict';

let type;
const table = 'currency_rates_monthly';

/**
 * We receive the dbmigrate dependency from dbmigrate initially.
 * This enables us to not have to rely on NODE_PATH.
 */
export const setup = (options) => {
  type = options.dbmigrate.dataType;
};

export const up = (db) =>
  db
    .createTable(table, {
      columns: {
        id: {
          autoIncrement: true,
          type: type.INTEGER,
          primaryKey: true,
        },
        currency_id: {
          type: type.INTEGER,
          length: 3,
          notNull: true,
          unsigned: true,
        },
        month_end_date: {
          type: type.STRING,
          length: 10,
          notNull: true,
        },
        rate: {
          type: type.DECIMAL,
          precision: '20',
          scale: '10',
          notNull: true,
        },
      },
      ifNotExists: true,
      charset: 'utf8mb4',
      collate: 'utf8mb4_unicode_ci',
      engine: 'InnoDB',
    })
    .then(() => db.addIndex(table, 'unique_currency_month', ['currency_id', 'month_end_date'], true));

export const down = (db) => db.dropTable(table);

export const _meta = {
  version: 1,
};
