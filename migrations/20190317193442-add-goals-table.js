'use strict';

let type;
const table = 'goals';

/**
 * We receive the dbmigrate dependency from dbmigrate initially.
 * This enables us to not have to rely on NODE_PATH.
 */
export const setup = (options) => {
  type = options.dbmigrate.dataType;
};

export const up = (db) =>
  db.createTable(table, {
    columns: {
      id: {
        type: type.STRING,
        length: 36,
      },
      uid: {
        type: type.STRING,
        length: 36,
        notNull: true,
        foreignKey: {
          name: 'goals_uid_fk',
          table: 'users',
          rules: {
            onDelete: 'CASCADE',
            onUpdate: 'RESTRICT',
          },
          mapping: 'id',
        },
      },
      currency: {
        type: type.INTEGER,
        length: 2,
        unsigned: true,
      },
      dividends: {
        type: type.INTEGER,
        length: 10,
        unsigned: true,
      },
      networth: {
        type: type.INTEGER,
        length: 10,
        unsigned: true,
      },
    },
    ifNotExists: true,
  });

export const down = (db) => db.dropTable(table);

export const _meta = {
  version: 1,
};
