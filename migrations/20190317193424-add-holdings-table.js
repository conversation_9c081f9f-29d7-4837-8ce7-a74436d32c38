'use strict';

let type;
const table = 'holdings';

/**
 * We receive the dbmigrate dependency from dbmigrate initially.
 * This enables us to not have to rely on NODE_PATH.
 */
export const setup = (options) => {
  type = options.dbmigrate.dataType;
};

export const up = (db) =>
  db.createTable(table, {
    columns: {
      id: {
        type: type.STRING,
        length: 36,
      },
      uid: {
        type: type.STRING,
        length: 36,
        notNull: true,
        foreignKey: {
          name: 'holdings_uid_fk',
          table: 'users',
          rules: {
            onDelete: 'CASCADE',
            onUpdate: 'RESTRICT',
          },
          mapping: 'id',
        },
      },
      currency: {
        type: type.INTEGER,
        length: 2,
        unsigned: true,
      },
      symbol: {
        type: type.STRING,
        length: 10,
        notNull: true,
      },
      name: {
        type: type.STRING,
        length: 255,
      },
      count: {
        type: type.DECIMAL,
        precision: '20',
        scale: '10',
      },
      price: {
        type: type.DECIMAL,
        precision: '20',
        scale: '10',
      },
      dividend: {
        type: type.DECIMAL,
        precision: '20',
        scale: '10',
      },
      dividendCurrency: {
        type: type.INTEGER,
        length: 2,
      },
      dividendFrequency: {
        type: type.INTEGER,
        length: 1,
      },
      position: type.INTEGER,
      isDeleted: type.BOOLEAN,
    },
    ifNotExists: true,
  });

export const down = (db) => db.dropTable(table);

export const _meta = {
  version: 1,
};
