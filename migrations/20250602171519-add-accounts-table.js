'use strict';

let type;
const table = 'accounts';

/**
 * We receive the dbmigrate dependency from dbmigrate initially.
 * This enables us to not have to rely on NODE_PATH.
 */
export const setup = (options) => {
  type = options.dbmigrate.dataType;
};

export const up = (db) =>
  db.createTable(table, {
    columns: {
      id: {
        autoIncrement: true,
        type: type.INTEGER,
        primaryKey: true,
      },
      uid: {
        type: type.STRING,
        length: 36,
        notNull: true,
      },
      type: {
        type: type.INTEGER,
        length: 1,
        notNull: true,
      },
      taxTreatment: {
        type: type.INTEGER,
        length: 1,
        notNull: true,
        defaultValue: 1,
      },
      withholdingTax: {
        type: type.INTEGER,
        length: 3,
      },
      name: {
        type: type.STRING,
        length: 255,
        notNull: true,
      },
      description: type.TEXT,
    },
    ifNotExists: true,
    charset: 'utf8mb4',
    collate: 'utf8mb4_unicode_ci',
    engine: 'MyISAM',
  });

export const down = (db) => db.dropTable(table);

export const _meta = {
  version: 1,
};
