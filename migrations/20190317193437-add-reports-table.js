'use strict';

let type;
const table = 'reports';

/**
 * We receive the dbmigrate dependency from dbmigrate initially.
 * This enables us to not have to rely on NODE_PATH.
 */
export const setup = (options) => {
  type = options.dbmigrate.dataType;
};

export const up = (db) =>
  db.createTable(table, {
    columns: {
      id: {
        type: type.STRING,
        length: 36,
      },
      uid: {
        type: type.STRING,
        length: 36,
        notNull: true,
        foreignKey: {
          name: 'reports_uid_fk',
          table: 'users',
          rules: {
            onDelete: 'CASCADE',
            onUpdate: 'RESTRICT',
          },
          mapping: 'id',
        },
      },
      year: {
        type: type.INTEGER,
        length: 4,
        unsigned: true,
      },
      month: {
        type: type.INTEGER,
        length: 2,
        unsigned: true,
      },
      isLocked: type.BOOLEAN,
      currencyRates: type.TEXT,
      holdings: type.TEXT,
    },
    ifNotExists: true,
  });

export const down = (db) => db.dropTable(table);

export const _meta = {
  version: 1,
};
