'use strict';

let type;
const table = 'users';

/**
 * We receive the dbmigrate dependency from dbmigrate initially.
 * This enables us to not have to rely on NODE_PATH.
 */
export const setup = (options) => {
  type = options.dbmigrate.dataType;
};

export const up = (db) =>
  db.createTable(table, {
    columns: {
      id: {
        type: type.STRING,
        length: 36,
        primaryKey: true,
      },
      currency: {
        type: type.INTEGER,
        length: 3,
        notNull: false,
        default: null,
      },
      dtCreated: {
        type: type.INTEGER,
        length: 10,
        unsigned: true,
      },
      dtLogin: {
        type: type.INTEGER,
        length: 10,
        unsigned: true,
      },
      email: {
        type: type.STRING,
        length: 80,
        unique: true,
      },
      jwt: {
        type: type.TEXT,
        length: 500,
      },
      licence: type.TEXT,
      otp: {
        type: type.STRING,
        length: 5,
        notNull: false,
      },
      otpExpires: {
        type: type.INTEGER,
        length: 10,
        notNull: false,
        unsigned: true,
      },
      pass: {
        type: type.STRING,
        length: 61,
      },
    },
    ifNotExists: true,
  });

export const down = (db) => db.dropTable(table);

export const _meta = {
  version: 1,
};
