services:
  redis:
    image: redis:alpine
    container_name: dt-redis
    restart: unless-stopped
    ports:
      - '6379:6379'
    networks:
      - local
  mysql:
    image: mysql:8.0
    container_name: dt-mysql
    restart: unless-stopped
    volumes:
      - type: volume
        source: db-data
        target: /var/lib/mysql
        volume:
          nocopy: true
    ports:
      - '3306:3306'
    environment:
      MYSQL_DATABASE: node_dt
      MYSQL_PASSWORD: Wdb7LuDJpwWumcB2
      MYSQL_ROOT_PASSWORD: p0w3rm0dz
      MYSQL_USER: dt
      SERVICE_TAGS: 'dev'
      SERVICE_NAME: 'mysql'
    networks:
      - local

volumes:
  db-data:

networks:
  local:
    driver: bridge
