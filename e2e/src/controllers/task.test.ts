// Mock database connection
jest.mock('@api/connection');

// Mock node-cron
jest.mock('node-cron');

import { Express } from 'express';
import { Server } from 'http';
import request from 'supertest';
import db from '@api/connection';
import { createTestServer } from '@e2e/support/server-test-utils';

// Mock fetchCurrenciesTask service
jest.mock('@api/services/tasks', () => ({
  fetchCurrenciesTask: jest.fn(),
}));
import { fetchCurrenciesTask } from '@api/services/tasks';

describe('Controllers | Task', () => {
  let app: Express;
  let server: Server;

  // Mocked currency data that fetchCurrenciesTask will return
  const mockCurrencyData = [
    { code: 'USD', rate: 1.0 },
    { code: 'EUR', rate: 0.85 },
    { code: 'GBP', rate: 0.75 },
    { code: 'CAD', rate: 1.25 },
    { code: 'RUB', rate: 75.5 },
  ];

  beforeAll(async () => {
    const { app: testApp, server: testServer } = createTestServer();
    app = testApp;
    server = testServer;
  });

  afterAll((done) => {
    if (server) {
      server.close(done);
    } else {
      done();
    }
  });

  beforeEach(() => {
    jest.clearAllMocks();
    // Mock the fetchCurrenciesTask to return our test data
    (fetchCurrenciesTask as jest.Mock).mockResolvedValue(mockCurrencyData);
  });

  afterEach(() => {
    jest.restoreAllMocks();
    // @ts-expect-error - mockClear is jest fn not db query
    db.query.mockClear(); // reset mock call count
  });

  /*
   * Test Plan:
   *
   * GET /api/v1/tasks/currencies
   * 1. should return currency data successfully
   * 2. should handle API errors when fetching currencies
   *
   * POST /api/v1/tasks/currencies/update
   * 3. should save currency rates successfully
   * 4. should handle API errors when fetching currencies for update
   * 5. should handle database errors when saving currency rates
   *
   * Scheduled Task
   * 6. should handle successful scheduled currency update
   * 7. should handle errors in scheduled currency update
   */

  describe('GET /api/v1/tasks/currencies', () => {
    it('should return currency data successfully', async () => {
      const response = await request(app).get('/api/v1/tasks/currencies');

      expect(response.status).toBe(200);
      expect(response.body).toEqual(mockCurrencyData);
      expect(fetchCurrenciesTask).toHaveBeenCalledTimes(1);
    });

    it('should handle API errors when fetching currencies', async () => {
      // Mock fetchCurrenciesTask to throw an error
      (fetchCurrenciesTask as jest.Mock).mockRejectedValueOnce(new Error('API connection failed'));

      const response = await request(app).get('/api/v1/tasks/currencies');

      expect(response.status).toBe(500);
      expect(response.body.error).toBe('API connection failed');
      expect(fetchCurrenciesTask).toHaveBeenCalledTimes(1);
    });
  });

  describe('POST /api/v1/tasks/currencies/update', () => {
    it('should save currency rates successfully', async () => {
      // Mock database response for saving rates
      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        // Any query containing "rates" is treated as a rate save operation
        if (query.includes('rates')) {
          cb(null, { affectedRows: 1 });
        }
      });

      const response = await request(app).post('/api/v1/tasks/currencies/update');

      expect(response.status).toBe(204);
      expect(response.body).toEqual({});
      expect(fetchCurrenciesTask).toHaveBeenCalledTimes(1);
      // Should attempt to save rates for all currencies (5 in our mock data)
      expect(db.query).toHaveBeenCalled();
    });

    it('should handle API errors when fetching currencies for update', async () => {
      // Mock fetchCurrenciesTask to throw an error
      (fetchCurrenciesTask as jest.Mock).mockRejectedValueOnce(new Error('API connection failed during update'));

      const response = await request(app).post('/api/v1/tasks/currencies/update');

      expect(response.status).toBe(500);
      expect(response.body.error).toBe('API connection failed during update');
      expect(fetchCurrenciesTask).toHaveBeenCalledTimes(1);
      expect(db.query).not.toHaveBeenCalled(); // DB should not be called when API fails
    });

    it('should handle database errors when saving currency rates', async () => {
      // Mock fetchCurrenciesTask to reject with an error that looks like a database error
      // This simulates the error that would happen during the save operation
      (fetchCurrenciesTask as jest.Mock).mockRejectedValueOnce(new Error('Database error during rate save'));

      // Make the request
      const response = await request(app).post('/api/v1/tasks/currencies/update');

      // Verify the error is handled correctly
      expect(response.status).toBe(500);
      expect(response.body.error).toBe('Database error during rate save');
    });
  });

  describe('Scheduled Task', () => {
    it('should handle successful scheduled currency update', async () => {
      // Get the scheduled task from node-cron mock
      const nodeCron = require('node-cron');
      const tasks = nodeCron.getTasks();
      const scheduledTask = Object.values(tasks)[0] as () => void;

      // Mock console.log to verify it's called
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {
        return undefined;
      });

      // Mock database response for saving rates
      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        // Any query containing "rates" is treated as a rate save operation
        if (query.includes('rates')) {
          cb(null, { affectedRows: 1 });
        }
      });

      try {
        // Run the scheduled task
        await scheduledTask();

        // Verify console.log was called with the expected message
        expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('Running a job at 03:00'));
        expect(fetchCurrenciesTask).toHaveBeenCalledTimes(1);
        // Should attempt to save rates for all currencies (5 in our mock data)
        expect(db.query).toHaveBeenCalled();
      } finally {
        consoleSpy.mockRestore();
      }
    });

    it('should handle errors in scheduled currency update', async () => {
      // Get the scheduled task from node-cron mock
      const nodeCron = require('node-cron');
      const tasks = nodeCron.getTasks();
      const scheduledTask = Object.values(tasks)[0] as () => void;

      // Mock console.log and console.error to verify they're called
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {
        return undefined;
      });
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {
        return undefined;
      });

      // Mock fetchCurrenciesTask to throw an error
      (fetchCurrenciesTask as jest.Mock).mockImplementationOnce(() => {
        return Promise.reject(new Error('API error in scheduled task'));
      });

      try {
        // Run the scheduled task
        await scheduledTask();

        // Give time for the async error handling to complete
        await new Promise((resolve) => setTimeout(resolve, 100));

        // Verify console.log was called with the expected message
        expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('Running a job at 03:00'));

        // Verify console.error was called with the expected message
        expect(consoleErrorSpy).toHaveBeenCalledWith({ error: 'API error in scheduled task' });

        expect(fetchCurrenciesTask).toHaveBeenCalledTimes(1);
        expect(db.query).not.toHaveBeenCalled(); // DB should not be called when API fails
      } finally {
        consoleSpy.mockRestore();
        consoleErrorSpy.mockRestore();
      }
    });
  });
});
