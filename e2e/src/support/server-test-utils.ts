import express, { Express } from 'express';
import session from 'express-session';
import request from 'supertest';
import { Server } from 'http';

import AccountController from '@api/controllers/account';
import AuthController from '@api/controllers/auth';
import GoalController from '@api/controllers/goal';
import HoldingController from '@api/controllers/holding';
import RateController from '@api/controllers/rate';
import ReportController from '@api/controllers/report';
import UserController from '@api/controllers/user';
import TaskController from '@api/controllers/task';

export const API_PREFIX = '/api/v1';

export interface TestResponse {
  body: any;
  status: number;
}

export interface TestServer {
  app: Express;
  server: Server;
}

const registerControllers = (app: Express) => {
  AccountController(app);
  AuthController(app);
  GoalController(app);
  HoldingController(app);
  RateController(app);
  ReportController(app);
  TaskController(app);
  UserController(app);
};

export const createTestServer = (): TestServer => {
  const app = express() as Express;
  app.use(express.json());
  app.use(express.urlencoded({ extended: true }));
  app.use(
    session({
      secret: 'test-secret',
      resave: false,
      saveUninitialized: true,
      cookie: { secure: false },
    }) as unknown as express.RequestHandler,
  );

  registerControllers(app);
  const server = app.listen(0); // Use port 0 to let the OS assign a random available port
  return { app, server };
};

export const makeRequest = (app: Express, controller: string) => {
  return {
    get: (path: string) => request(app).get(`${API_PREFIX}/${controller}${path}`),
    post: (path: string) => request(app).post(`${API_PREFIX}/${controller}${path}`),
    put: (path: string) => request(app).put(`${API_PREFIX}/${controller}${path}`),
    delete: (path: string) => request(app).delete(`${API_PREFIX}/${controller}${path}`),
  };
};

export const expectErrorResponse = (response: TestResponse, code: string, status: number) => {
  expect(response.body).toEqual({
    error: {
      code,
      message: `Error code: ${code}`,
    },
  });
  expect(response.status).toBe(status);
};

export const expectSuccessResponse = <T>(response: TestResponse, data: T) => {
  expect(response.body).toEqual(data);
  expect(response.status).toBe(200);
};
