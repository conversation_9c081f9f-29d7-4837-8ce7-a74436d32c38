jest.mock('@api/connection');
import db from '@api/connection';
import ReportModel from '@api/models/report';
import { toJson } from '@api/lib/utils';
import { escapeKey, getKeys, sqlWhere } from '@api/lib/test-helpers';

const date = new Date();
const currentYear = date.getFullYear();
const currentMonth = date.getMonth();

const createModelObject = (attrs: Partial<ReportJson> | undefined = {}) =>
  ({
    id: undefined,
    uid: 'test-user-id',
    year: currentYear,
    month: currentMonth,
    holdings: [],
    isLocked: false,

    ...attrs,
  }) as ReportJson;

describe('Models | Report', () => {
  const table = 'reports';
  let instance: ReportI;

  beforeEach(() => {
    instance = ReportModel(createModelObject());
  });

  afterEach(() => {
    jest.restoreAllMocks();
    // @ts-expect-error - expected
    db.query.mockClear(); // reset mock call count
  });

  it('should be defined', () => {
    expect(ReportModel).toBeDefined();
    expect(instance).toBeDefined();
  });

  it('should mark an instance without id as new', () => {
    const reportA = ReportModel();
    const reportB = ReportModel({ id: '1' });

    expect(reportA.isNew).toBe(true);
    expect(reportB.isNew).toBe(false);
  });

  it('should not list special attributes', () => {
    expect(instance).toEqual(createModelObject());
    expect(instance.toJson()).toEqual(createModelObject());
  });

  it('should use a nonenumerable method to return JSON structure of the model', () => {
    expect(instance.toJson()).toEqual(toJson(instance, { hidden: ['pass'] }));
  });

  it('should use a nonenumerable method "save" for new and existing models', async () => {
    const id = 'test-id-1';
    const uid = 'test-uid-1';
    const modelNew = ReportModel(createModelObject({ uid }));
    const modelExisting = ReportModel(createModelObject({ id, uid }));

    try {
      await modelNew.save();
      await modelExisting.save();
    } catch (error) {
      console.log(error);
    }

    const newkeys = getKeys(modelNew);
    const newValues = Object.values(modelNew);
    const newReplacements = Object.values(modelNew)
      .map(() => '?')
      .join(', ');
    const dataQuery = Object.keys(modelExisting)
      .map((key) => `${escapeKey(key)}=?`)
      .join(', ');

    expect(db.query).toHaveBeenCalledTimes(2);
    expect(db.query).toHaveBeenNthCalledWith(
      1,
      `INSERT INTO ${table} (${newkeys}) VALUES (${newReplacements});`,
      newValues,
      expect.any(Function),
    );
    expect(db.query).toHaveBeenNthCalledWith(
      2,
      `UPDATE ${table} SET ${dataQuery} ${sqlWhere({ id: modelExisting.id, uid: modelExisting.uid })};`,
      [...Object.values(modelExisting), modelExisting.id, modelExisting.uid],
      expect.any(Function),
    );
  });

  it('should execute a correct query for static method "findOne"', async () => {
    const id = 'test-id';

    try {
      await ReportModel.findOne({ where: { id } });
    } catch (error) {
      console.log(error);
    }

    expect(db.query).toHaveBeenCalledTimes(1);
    expect(db.query).toHaveBeenNthCalledWith(1, `SELECT * FROM ${table} ${sqlWhere({ id })} LIMIT 1;`, [id], expect.any(Function));
  });

  it('should execute a correct query for static method "findAll"', async () => {
    const uid = 'test-id';

    try {
      await ReportModel.findAll({ where: { uid } });
    } catch (error) {
      console.log(error);
    }

    expect(db.query).toHaveBeenCalledTimes(1);
    expect(db.query).toHaveBeenNthCalledWith(1, `SELECT * FROM ${table} ${sqlWhere({ uid })};`, [uid], expect.any(Function));
  });

  it('should execute a correct query for static method "delete"', async () => {
    const { id = 'test-id-1', uid } = instance;

    try {
      await ReportModel.delete({ id, uid });
    } catch (error) {
      console.log(error);
    }

    expect(db.query).toHaveBeenCalledTimes(1);
    expect(db.query).toHaveBeenNthCalledWith(1, `DELETE FROM ${table} ${sqlWhere({ id, uid })};`, [id, uid], expect.any(Function));
  });

  it('should execute a correct query for static method "deleteAll"', async () => {
    const { uid } = instance;

    try {
      await ReportModel.deleteAll(uid);
    } catch (error) {
      console.log(error);
    }

    expect(db.query).toHaveBeenCalledTimes(1);
    expect(db.query).toHaveBeenNthCalledWith(1, `DELETE FROM ${table} ${sqlWhere({ uid })};`, [uid], expect.any(Function));
  });
});
