jest.mock('@api/connection');
import db from '@api/connection';
import RateModel from '@api/models/rate';
import { toJson } from '@api/lib/utils';
import { escapeKey, sqlWhere } from '@api/lib/test-helpers';

const createModelObject = (attrs: Partial<RateJson> | undefined = {}) =>
  ({
    id: 1,
    code: 'CAD',
    dtUpdated: Math.floor(Date.now() / 1000),
    rate: 1.33,

    ...attrs
  }) as RateJson;

describe('Models | Rate', () => {
  const table = 'currency_rates';
  let instance: RateI;

  beforeEach(() => {
    instance = RateModel(createModelObject());
  });

  afterEach(() => {
    jest.restoreAllMocks();
    // @ts-expect-error - expected
    db.query.mockClear(); // reset mock call count
  });

  it('should be defined', () => {
    expect(RateModel).toBeDefined();
    expect(instance).toBeDefined();
  });

  it('should not list special attributes', () => {
    expect(instance).toEqual(createModelObject());
    expect(instance.toJson()).toEqual(createModelObject());
  });

  it('should use a nonenumerable method to return JSON structure of the model', () => {
    expect(instance.toJson()).toEqual(toJson(instance, { hidden: ['pass'] }));
  });

  it('should use a nonenumerable method "save" correctly', async () => {
    const model = RateModel(createModelObject({}));

    try {
      await model.save();
    } catch (error) {
      console.log(error);
    }

    const values = Object.values(model);
    const dataQuery = Object.keys(model)
      .map((key) => `${escapeKey(key)}=?`)
      .join(', ');

    expect(db.query).toHaveBeenCalledTimes(1);
    expect(db.query).toHaveBeenNthCalledWith(
      1,
      `UPDATE ${table} SET ${dataQuery} ${sqlWhere({ id: model.id })};`,
      [...values, model.id],
      expect.any(Function)
    );
  });

  it('should execute a correct query for static method "findOne"', async () => {
    const id = 'test-id';

    try {
      await RateModel.findOne({ where: { id } });
    } catch (error) {
      console.log(error);
    }

    expect(db.query).toHaveBeenCalledTimes(1);
    expect(db.query).toHaveBeenNthCalledWith(1, `SELECT * FROM ${table} ${sqlWhere({ id })} LIMIT 1;`, [id], expect.any(Function));
  });

  it('should execute a correct query for static method "findAll"', async () => {
    const uid = 'test-id';

    try {
      await RateModel.findAll({ where: { uid } });
    } catch (error) {
      console.log(error);
    }

    expect(db.query).toHaveBeenCalledTimes(1);
    expect(db.query).toHaveBeenNthCalledWith(1, `SELECT * FROM ${table} ${sqlWhere({ uid })};`, [uid], expect.any(Function));
  });
});
