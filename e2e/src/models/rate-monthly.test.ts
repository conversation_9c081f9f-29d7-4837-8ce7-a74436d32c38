jest.mock('@api/connection');
import db from '@api/connection';
import RateMonthlyModel from '@api/models/rate-monthly';
import { toJson } from '@api/lib/utils';
import { escapeKey, sqlWhere } from '@api/lib/test-helpers';

const createModelObject = (attrs: Partial<RateMonthlyJson> | undefined = {}) =>
  ({
    currency_id: 1,
    id: 1,
    month_end_date: '2024-02-29',
    rate: 1.3456789012,

    ...attrs,
  }) as RateMonthlyJson;

describe('Models | RateMonthly', () => {
  const table = 'currency_rates_monthly';
  let instance: RateMonthlyI;

  beforeEach(() => {
    instance = RateMonthlyModel(createModelObject());
  });

  afterEach(() => {
    jest.restoreAllMocks();
    // @ts-expect-error - expected
    db.query.mockClear(); // reset mock call count
  });

  it('should be defined', () => {
    expect(RateMonthlyModel).toBeDefined();
    expect(instance).toBeDefined();
  });

  it('should not list special attributes', () => {
    expect(instance).toEqual(createModelObject());
    expect(instance.toJson()).toEqual(createModelObject());
  });

  it('should use a nonenumerable method to return JSON structure of the model', () => {
    expect(instance.toJson()).toEqual(toJson(instance));
  });

  it('should mark an instance without id as new', () => {
    const rateMonthlyA = RateMonthlyModel({ currency_id: 1, month_end_date: '2024-02-29', rate: 1.33 });
    const rateMonthlyB = RateMonthlyModel({ id: 1, currency_id: 1, month_end_date: '2024-02-29', rate: 1.33 });

    expect(rateMonthlyA.isNew).toBe(true);
    expect(rateMonthlyB.isNew).toBe(false);
  });

  it('should use a nonenumerable method "save" correctly for new record', async () => {
    const modelData = { currency_id: 1, month_end_date: '2024-02-29', rate: 1.3456789012 };
    const model = RateMonthlyModel(modelData);

    // @ts-expect-error - mockImplementation is jest fn not db query
    db.query.mockImplementation((query, params, cb) => {
      cb(null, { insertId: 123 });
    });

    try {
      await model.save();
    } catch (error) {
      console.log(error);
    }

    const expectedValues = [modelData.currency_id, modelData.month_end_date, modelData.rate];

    expect(db.query).toHaveBeenCalledTimes(1);
    expect(db.query).toHaveBeenNthCalledWith(
      1,
      'INSERT INTO currency_rates_monthly (`currency_id`, `month_end_date`, `rate`) VALUES (?, ?, ?);',
      expectedValues,
      expect.any(Function),
    );
    expect(model.id).toBe(123);
  });

  it('should use a nonenumerable method "save" correctly for existing record', async () => {
    const model = RateMonthlyModel(createModelObject({}));

    try {
      await model.save();
    } catch (error) {
      console.log(error);
    }

    const values = Object.values(model);
    const dataQuery = Object.keys(model)
      .map((key) => `${escapeKey(key)}=?`)
      .join(', ');

    expect(db.query).toHaveBeenCalledTimes(1);
    expect(db.query).toHaveBeenNthCalledWith(
      1,
      `UPDATE ${table} SET ${dataQuery} ${sqlWhere({ id: model.id })};`,
      [...values, model.id],
      expect.any(Function),
    );
  });

  it('should execute a correct query for static method "findOne"', async () => {
    const id = 1;

    // @ts-expect-error - mockImplementation is jest fn not db query
    db.query.mockImplementation((query, params, cb) => {
      cb(null, [createModelObject({ id })]);
    });

    try {
      await RateMonthlyModel.findOne({ where: { id } });
    } catch (error) {
      console.log(error);
    }

    expect(db.query).toHaveBeenCalledTimes(1);
    expect(db.query).toHaveBeenNthCalledWith(1, `SELECT * FROM ${table} ${sqlWhere({ id })} LIMIT 1;`, [id], expect.any(Function));
  });

  it('should return null when findOne finds no record', async () => {
    const id = 999;

    // @ts-expect-error - mockImplementation is jest fn not db query
    db.query.mockImplementation((query, params, cb) => {
      cb(null, null);
    });

    let result;
    try {
      result = await RateMonthlyModel.findOne({ where: { id } });
    } catch (error) {
      console.log(error);
    }

    expect(result).toBeNull();
    expect(db.query).toHaveBeenCalledTimes(1);
  });

  it('should execute a correct query for static method "findAll" with date filter for 2024-02-29', async () => {
    const month_end_date = '2024-02-29';

    // @ts-expect-error - mockImplementation is jest fn not db query
    db.query.mockImplementation((query, params, cb) => {
      cb(null, []);
    });

    try {
      await RateMonthlyModel.findAll({ where: { month_end_date } });
    } catch (error) {
      console.log(error);
    }

    expect(db.query).toHaveBeenCalledTimes(1);
    expect(db.query).toHaveBeenNthCalledWith(
      1,
      `SELECT * FROM ${table} ${sqlWhere({ month_end_date })};`,
      [month_end_date],
      expect.any(Function),
    );
  });

  it('should execute a correct query for static method "findAll" without filters', async () => {
    // @ts-expect-error - mockImplementation is jest fn not db query
    db.query.mockImplementation((query, params, cb) => {
      cb(null, []);
    });

    try {
      await RateMonthlyModel.findAll();
    } catch (error) {
      console.log(error);
    }

    expect(db.query).toHaveBeenCalledTimes(1);
    expect(db.query).toHaveBeenNthCalledWith(1, `SELECT * FROM ${table};`, [], expect.any(Function));
  });

  it('should return array of RateMonthly instances when findAll returns records', async () => {
    const mockRecords = [
      createModelObject({ id: 1, currency_id: 1, month_end_date: '2024-02-29', rate: 1.3456 }),
      createModelObject({ id: 2, currency_id: 2, month_end_date: '2024-02-29', rate: 0.8765 }),
    ];

    // @ts-expect-error - mockImplementation is jest fn not db query
    db.query.mockImplementation((query, params, cb) => {
      cb(null, mockRecords);
    });

    let result;
    try {
      result = await RateMonthlyModel.findAll({ where: { month_end_date: '2024-02-29' } });
    } catch (error) {
      console.log(error);
    }

    expect(result).toHaveLength(2);
    expect(result[0]).toHaveProperty('currency_id', 1);
    expect(result[0]).toHaveProperty('month_end_date', '2024-02-29');
    expect(result[0]).toHaveProperty('rate', 1.3456);
    expect(result[0]).toHaveProperty('toJson');
    expect(result[0]).toHaveProperty('save');
    expect(result[1]).toHaveProperty('currency_id', 2);
    expect(result[1]).toHaveProperty('rate', 0.8765);
  });
});
