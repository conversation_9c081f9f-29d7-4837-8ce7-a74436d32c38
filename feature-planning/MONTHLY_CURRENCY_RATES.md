v1.7.0

files for context:

- step 1 - implement migration file:
  - migrations/20250604025529-add-currency-rates-monthly-table.js (refactor to use moder javascript, following the pattern of other migrations)
- step 2 - implement CurrencyRatesMonthly model and CurrencyRatesMonthlyI types:
  - src/models/rate-monthly/index.ts (following the pattern of other models)
- step 3 - implement RatesMonthly controller:
  - src/services/tasks/index.ts (refactor to use modern javascript, following the pattern of other tasks)
  - src/services/tasks/index.test.ts (refactor to use modern javascript, following the pattern of other tests)

# Currency Database Design Summary

## Overview

Simple 2-table approach for storing current currency rates with historical end-of-month snapshots. Designed for minimal complexity while maintaining data integrity.

## Database Structure

### Table 1: `currency_rates` (Current Rates)

**Purpose**: Master currencies table + current daily rates

```sql
-- Existing structure (no changes needed)
CREATE TABLE `currency_rates` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `code` VARCHAR(3) NOT NULL,
  `dtUpdated` INT NOT NULL,
  `rate` DECIMAL(12,8) NOT NULL
);
```

**Current Data**:

- USD, CAD, RUB, EUR, GBP, etc
- Updated daily with latest rates
- Serves as master currency reference

### Table 2: `currency_rates_monthly` (Historical Snapshots)

**Purpose**: End-of-month historical data only

```sql
CREATE TABLE `currency_rates_monthly` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `currency_id` INT NOT NULL,
  `month_end_date` VARCHAR(10) NOT NULL, -- Format: 'YYYY-MM-DD'
  `rate` DECIMAL(20,10) NOT NULL,
  UNIQUE KEY `unique_currency_month` (`currency_id`, `month_end_date`),
  FOREIGN KEY (`currency_id`) REFERENCES `currency_rates`(`id`)
);
```

**Key Features**:

- Foreign key relationship to `currency_rates.id`
- Date stored as string (`'2025-02-28'`) to avoid timezone issues
- Only end-of-month snapshots (not daily pollution)
- Unique constraint prevents duplicate month/currency combinations

## Data Management Process

### Daily Operations

- Update `currency_rates` table with latest rates
- No changes to historical table

### Monthly Snapshots

Run at end of each month:

```sql
INSERT INTO currency_rates_monthly (currency_id, month_end_date, rate)
SELECT id, '2025-02-28', rate
FROM currency_rates
ON DUPLICATE KEY UPDATE rate = VALUES(rate);
```

## Query Examples

### Get Current Rates

```sql
SELECT code, rate, dtUpdated
FROM currency_rates;
```

### Get Historical Rate for Specific Month

```sql
SELECT cr.code, crm.rate, crm.month_end_date
FROM currency_rates_monthly crm
JOIN currency_rates cr ON crm.currency_id = cr.id
WHERE crm.month_end_date = '2025-01-31';
```

### Get Rate History for Specific Currency

```sql
SELECT crm.month_end_date, crm.rate
FROM currency_rates_monthly crm
JOIN currency_rates cr ON crm.currency_id = cr.id
WHERE cr.code = 'EUR'
ORDER BY crm.month_end_date DESC;
```

## Benefits

✅ **Simple**: Only 2 tables, minimal complexity  
✅ **Clean**: No daily data pollution in historical table  
✅ **Efficient**: Fast queries, proper indexing  
✅ **Maintainable**: Clear separation of current vs historical  
✅ **Timezone-safe**: String dates avoid timezone complications  
✅ **Normalized**: Foreign key relationship maintains data integrity

## Storage Efficiency

- **Current rates**: ~5-50 records (number of currencies)
- **Historical rates**: ~12 records per currency per year
- **Total annual growth**: ~60-600 records (very manageable)
