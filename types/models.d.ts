export {};

declare global {
  type ErrorCodeI = {
    code: string | number | null;
    message: string;
  };

  type CurrencyCode = 'CAD' | 'EUR' | 'GBP' | 'RUB' | 'USD' | 'JPY' | 'CNY' | 'AUD' | 'CHF' | 'INR';
  type CurrencySymbol = 'c$' | '€' | '£' | '₽' | '$' | 'JP¥' | '¥' | 'a$' | '₣' | '₹';

  type Currency = {
    id: number;
    code: CurrencyCode;
    symbol: CurrencySymbol;
  };

  type AccountJson = {
    description?: string;
    id: number;
    name: string;
    taxTreatment: number;
    type: number;
    uid: string;
    withholdingTax?: number;
  };

  type AccountI = AccountJson & {
    import: () => Promise<AccountI>;
    isNew: boolean;
    save: () => Promise<AccountI>;
    toJson: () => AccountJson;
  };

  type GoalJson = {
    currency: number;
    dividends: number;
    id: string;
    networth: number;
    uid: string;
  };

  type GoalI = GoalJson & {
    import: () => Promise<GoalI>;
    isNew: boolean;
    save: () => Promise<GoalI>;
    toJson: () => GoalJson;
  };

  type HoldingJson = {
    count: number;
    currency: number;
    dividend: number;
    dividendCurrency: number;
    dividendFrequency: number;
    dividendTaxRate: number;
    id: string;
    isDeleted: boolean;
    name: string;
    position: number;
    price: number;
    symbol: string;
    uid: string;
  };

  type HoldingI = HoldingJson & {
    import: () => Promise<HoldingI>;
    isNew: boolean;
    save: () => Promise<HoldingI>;
    toJson: () => HoldingJson;
  };

  type RateJson = {
    code: CurrencyCode;
    dtUpdated: number;
    id: number;
    rate: number;
  };

  type RateI = RateJson & {
    save: () => Promise<RateI>;
    toJson: () => RateJson;
  };

  type ReportHoldingI = {
    count: number;
    currency:
      | CURRENCY_CAD
      | CURRENCY_EUR
      | CURRENCY_GBP
      | CURRENCY_RUB
      | CURRENCY_USD
      | CURRENCY_JPY
      | CURRENCY_CNY
      | CURRENCY_AUD
      | CURRENCY_CHF
      | CURRENCY_INR;
    dividend: number;
    dividendCurrency:
      | CURRENCY_CAD
      | CURRENCY_EUR
      | CURRENCY_GBP
      | CURRENCY_RUB
      | CURRENCY_USD
      | CURRENCY_JPY
      | CURRENCY_CNY
      | CURRENCY_AUD
      | CURRENCY_CHF
      | CURRENCY_INR;
    dividends: number;
    id: string;
    position: number;
    price: number;
    symbol: string;
  };

  type ReportJson = {
    id: string;
    uid: string;
    year: number;
    month: number;
    holdings: ReportHoldingI[];
    isLocked: boolean;
  };

  type ReportI = ReportJson & {
    currencyRates: object;
    converted: {
      dividends: number;
      networth: number;
    };

    import: () => Promise<ReportI>;
    isNew: boolean;
    save: () => Promise<ReportI>;
    toJson: () => ReportJson;
  };

  type UserJson = {
    currency: number;
    dtCreated: number | null;
    dtLogin: number | null;
    email: string;
    id: string;
    licence: string;
  };

  type UserI = UserJson & {
    isNew: boolean;
    jwt: string;
    otp: string;
    otpExpires: number;
    pass: string;
    save: () => Promise<UserI>;
    toJson: () => UserJson;
  };
}
